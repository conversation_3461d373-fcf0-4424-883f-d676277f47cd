==> 2025-11-10 02:19:49 <==
# cmd: C:\Users\<USER>\anaconda3\Lib\site-packages\conda\__main__.py create --yes --prefix .conda python=3.11
# conda version: 24.11.3
+defaults/noarch::pip-25.2-pyhc872135_1
+defaults/noarch::tzdata-2025b-h04d1e81_0
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::ca-certificates-2025.11.4-haa95532_0
+defaults/win-64::expat-2.7.3-h9214b88_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::libzlib-1.3.1-h02ab6af_0
+defaults/win-64::openssl-3.0.18-h543e019_0
+defaults/win-64::python-3.11.14-h981015d_0
+defaults/win-64::setuptools-80.9.0-py311haa95532_0
+defaults/win-64::sqlite-3.51.0-hda9a48d_0
+defaults/win-64::tk-8.6.15-hf199647_0
+defaults/win-64::ucrt-10.0.22621.0-haa95532_0
+defaults/win-64::vc-14.3-h2df5915_10
+defaults/win-64::vc14_runtime-14.44.35208-h4927774_10
+defaults/win-64::vs2015_runtime-14.44.35208-ha6b5a95_10
+defaults/win-64::wheel-0.45.1-py311haa95532_0
+defaults/win-64::xz-5.6.4-h4754444_1
+defaults/win-64::zlib-1.3.1-h02ab6af_0
# update specs: ['python=3.11']
