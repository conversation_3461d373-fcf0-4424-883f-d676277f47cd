# Hull Tactical Market Prediction - Simple EDA
# Run this file with: python eda_simple.py

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

print("=== Hull Tactical Market Prediction - EDA ===")
print("Libraries imported successfully!")
print()

# Load data
print("Loading data...")
data = pd.read_csv('train.csv')
print(f"Data shape: {data.shape}")
print("Data loaded successfully!")
print()

# Display first few rows
print("=== First 5 rows ===")
print(data.head())
print()

# Basic info
print("=== Data Info ===")
print(data.info())
print()

# Basic statistics
print("=== Basic Statistics ===")
print(data.describe())
print()

# Check for missing values
print("=== Missing Values ===")
missing_values = data.isnull().sum()
missing_cols = missing_values[missing_values > 0]
if len(missing_cols) > 0:
    print("Missing values per column:")
    print(missing_cols)
else:
    print("No missing values found!")
print()

# Column names
print("=== Column Information ===")
print(f"Total columns: {len(data.columns)}")
print("Column names:")
for i, col in enumerate(data.columns):
    print(f"{i+1:2d}. {col}")
print()

# Target variable analysis
if 'forward_returns' in data.columns:
    print("=== Target Variable Analysis ===")
    print("Forward Returns statistics:")
    print(data['forward_returns'].describe())
    print()
    
    # Save a simple plot
    plt.figure(figsize=(10, 6))
    plt.hist(data['forward_returns'], bins=50, alpha=0.7, edgecolor='black')
    plt.title('Distribution of Forward Returns')
    plt.xlabel('Forward Returns')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)
    plt.savefig('forward_returns_distribution.png', dpi=300, bbox_inches='tight')
    print("Plot saved as 'forward_returns_distribution.png'")
    plt.show()

print("=== EDA Complete ===")
print("Add your own analysis below this line or in a new file!")
