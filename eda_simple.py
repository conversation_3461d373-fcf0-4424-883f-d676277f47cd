# Hull Tactical Market Prediction - Simple EDA
# Run this file with: python eda_simple.py

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

print("=== Hull Tactical Market Prediction - EDA ===")
print("Libraries imported successfully!")
print()

# Load data
print("Loading data...")
data = pd.read_csv('train.csv')
print(f"Data shape: {data.shape}")
print("Data loaded successfully!")
print()

# Display first few rows
print("=== First 5 rows ===")
print(data.head())
print()

# Basic info
print("=== Data Info ===")
print(data.info())
print()

# Basic statistics
print("=== Basic Statistics ===")
print(data.describe())
print()

# Check for missing values
print("=== Missing Values ===")
missing_values = data.isnull().sum()
missing_cols = missing_values[missing_values > 0]
if len(missing_cols) > 0:
    print("Missing values per column:")
    print(missing_cols)
else:
    print("No missing values found!")
print()

# Column names
print("=== Column Information ===")
print(f"Total columns: {len(data.columns)}")
print("Column names:")
for i, col in enumerate(data.columns):
    print(f"{i+1:2d}. {col}")
print()

# Target variable analysis
if 'forward_returns' in data.columns:
    print("=== Target Variable Analysis ===")
    print("Forward Returns statistics:")
    print(data['forward_returns'].describe())
    print()
    
    # Save a simple plot
    plt.figure(figsize=(10, 6))
    plt.hist(data['forward_returns'], bins=50, alpha=0.7, edgecolor='black')
    plt.title('Distribution of Forward Returns')
    plt.xlabel('Forward Returns')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)
    plt.savefig('forward_returns_distribution.png', dpi=300, bbox_inches='tight')
    print("Plot saved as 'forward_returns_distribution.png'")
    plt.show()

print("=== EDA Complete ===")
print()

# =============================================================================
# ADDITIONAL ANALYSIS - Data Processing and Correlation Analysis
# =============================================================================

print("=== STARTING SEQUENTIAL DATA PROCESSING ===")
print()

# Initialize the base dataframe for sequential edits
df_base = data.copy()
print(f"Base dataframe initialized: {df_base.shape}")
print()

# =============================================================================
# EDIT 1: Remove columns with more than 50% missing values
# =============================================================================

print("=== EDIT 1: Removing columns with >50% missing values ===")

# Calculate missing value percentages
missing_percentages = (df_base.isnull().sum() / len(df_base)) * 100
print("Missing value percentages by column:")
print(missing_percentages.sort_values(ascending=False))
print()

# Identify columns with more than 50% missing values
high_missing_cols = missing_percentages[missing_percentages > 50].index.tolist()
print(f"Columns with >50% missing values ({len(high_missing_cols)} columns):")
for col in high_missing_cols:
    print(f"  - {col}: {missing_percentages[col]:.1f}% missing")
print()

# Create dataframe after removing high missing columns
df_edit1 = df_base.drop(columns=high_missing_cols)
print(f"After removing high missing columns: {df_edit1.shape}")
print(f"Removed {len(high_missing_cols)} columns")
print()

# Show remaining missing values
remaining_missing = (df_edit1.isnull().sum() / len(df_edit1)) * 100
remaining_missing = remaining_missing[remaining_missing > 0]
if len(remaining_missing) > 0:
    print("Remaining columns with missing values:")
    print(remaining_missing.sort_values(ascending=False))
else:
    print("No missing values remaining!")
print()

# =============================================================================
# EDIT 2: Calculate correlations with target and feature-to-feature correlations
# =============================================================================

print("=== EDIT 2: Correlation Analysis ===")

# Target variable for correlation analysis
target_col = 'market_forward_excess_returns'
if target_col not in df_edit1.columns:
    print(f"Warning: Target column '{target_col}' not found!")
    target_col = 'forward_returns'  # fallback
    print(f"Using '{target_col}' as target instead")

# Calculate correlations with target variable
print(f"Calculating correlations with target variable: {target_col}")
target_correlations = df_edit1.corr()[target_col].abs().sort_values(ascending=False)

print("Top 20 features most correlated with target:")
print(target_correlations.head(20))
print()

print("Bottom 10 features least correlated with target:")
print(target_correlations.tail(10))
print()

# Create dataframe with correlation information
df_edit2 = df_edit1.copy()
df_edit2_corr_info = pd.DataFrame({
    'feature': target_correlations.index,
    'abs_correlation_with_target': target_correlations.values
})

print("Correlation summary statistics:")
print(f"Mean absolute correlation with target: {target_correlations.mean():.4f}")
print(f"Median absolute correlation with target: {target_correlations.median():.4f}")
print(f"Max absolute correlation with target: {target_correlations.max():.4f}")
print(f"Min absolute correlation with target: {target_correlations.min():.4f}")
print()

# Calculate feature-to-feature correlation matrix
print("Calculating feature-to-feature correlation matrix...")
correlation_matrix = df_edit2.corr()

# Find highly correlated feature pairs (excluding self-correlations)
print("Finding highly correlated feature pairs (|correlation| > 0.8):")
high_corr_pairs = []
for i in range(len(correlation_matrix.columns)):
    for j in range(i+1, len(correlation_matrix.columns)):
        corr_val = correlation_matrix.iloc[i, j]
        if abs(corr_val) > 0.8:
            high_corr_pairs.append({
                'feature1': correlation_matrix.columns[i],
                'feature2': correlation_matrix.columns[j],
                'correlation': corr_val
            })

if high_corr_pairs:
    high_corr_df = pd.DataFrame(high_corr_pairs)
    high_corr_df = high_corr_df.sort_values('correlation', key=abs, ascending=False)
    print(f"Found {len(high_corr_pairs)} highly correlated pairs:")
    print(high_corr_df.head(10))
else:
    print("No highly correlated pairs found (|correlation| > 0.8)")
print()

# Save correlation matrix to file
correlation_matrix.to_csv('correlation_matrix.csv')
print("Full correlation matrix saved as 'correlation_matrix.csv'")

# Save target correlations to file
target_correlations.to_csv('target_correlations.csv')
print("Target correlations saved as 'target_correlations.csv'")
print()

# =============================================================================
# SUMMARY OF SEQUENTIAL EDITS
# =============================================================================

print("=== SUMMARY OF SEQUENTIAL EDITS ===")
print(f"Original dataframe (df_base): {df_base.shape}")
print(f"After Edit 1 (df_edit1): {df_edit1.shape} - Removed {len(high_missing_cols)} high-missing columns")
print(f"After Edit 2 (df_edit2): {df_edit2.shape} - Added correlation analysis")
print()
print("Available dataframes:")
print("- df_base: Original data")
print("- df_edit1: Data after removing high-missing columns")
print("- df_edit2: Data with correlation analysis completed")
print("- df_edit2_corr_info: Correlation information dataframe")
print()
print("Files created:")
print("- forward_returns_distribution.png")
print("- correlation_matrix.csv")
print("- target_correlations.csv")
print()
print("=== ANALYSIS COMPLETE ===")
print("You can now add more analysis or use the processed dataframes!")
