# Automatically generated by scripts/gen_mapfiles.py.
# DO NOT EDIT BY HAND; run `tox -e mapfiles` instead.

STYLES = {
    'AbapStyle': ('pygments.styles.abap', 'abap', ()),
    'AlgolStyle': ('pygments.styles.algol', 'algol', ()),
    'Algol_NuStyle': ('pygments.styles.algol_nu', 'algol_nu', ()),
    'ArduinoStyle': ('pygments.styles.arduino', 'arduino', ()),
    'AutumnStyle': ('pygments.styles.autumn', 'autumn', ()),
    'BlackWhiteStyle': ('pygments.styles.bw', 'bw', ()),
    'BorlandStyle': ('pygments.styles.borland', 'borland', ()),
    'CoffeeStyle': ('pygments.styles.coffee', 'coffee', ()),
    'ColorfulStyle': ('pygments.styles.colorful', 'colorful', ()),
    'DefaultStyle': ('pygments.styles.default', 'default', ()),
    'DraculaStyle': ('pygments.styles.dracula', 'dracula', ()),
    'EmacsStyle': ('pygments.styles.emacs', 'emacs', ()),
    'FriendlyGrayscaleStyle': ('pygments.styles.friendly_grayscale', 'friendly_grayscale', ()),
    'FriendlyStyle': ('pygments.styles.friendly', 'friendly', ()),
    'FruityStyle': ('pygments.styles.fruity', 'fruity', ()),
    'GhDarkStyle': ('pygments.styles.gh_dark', 'github-dark', ()),
    'GruvboxDarkStyle': ('pygments.styles.gruvbox', 'gruvbox-dark', ()),
    'GruvboxLightStyle': ('pygments.styles.gruvbox', 'gruvbox-light', ()),
    'IgorStyle': ('pygments.styles.igor', 'igor', ()),
    'InkPotStyle': ('pygments.styles.inkpot', 'inkpot', ()),
    'LightbulbStyle': ('pygments.styles.lightbulb', 'lightbulb', ()),
    'LilyPondStyle': ('pygments.styles.lilypond', 'lilypond', ()),
    'LovelaceStyle': ('pygments.styles.lovelace', 'lovelace', ()),
    'ManniStyle': ('pygments.styles.manni', 'manni', ()),
    'MaterialStyle': ('pygments.styles.material', 'material', ()),
    'MonokaiStyle': ('pygments.styles.monokai', 'monokai', ()),
    'MurphyStyle': ('pygments.styles.murphy', 'murphy', ()),
    'NativeStyle': ('pygments.styles.native', 'native', ()),
    'NordDarkerStyle': ('pygments.styles.nord', 'nord-darker', ()),
    'NordStyle': ('pygments.styles.nord', 'nord', ()),
    'OneDarkStyle': ('pygments.styles.onedark', 'one-dark', ()),
    'ParaisoDarkStyle': ('pygments.styles.paraiso_dark', 'paraiso-dark', ()),
    'ParaisoLightStyle': ('pygments.styles.paraiso_light', 'paraiso-light', ()),
    'PastieStyle': ('pygments.styles.pastie', 'pastie', ()),
    'PerldocStyle': ('pygments.styles.perldoc', 'perldoc', ()),
    'RainbowDashStyle': ('pygments.styles.rainbow_dash', 'rainbow_dash', ()),
    'RrtStyle': ('pygments.styles.rrt', 'rrt', ()),
    'SasStyle': ('pygments.styles.sas', 'sas', ()),
    'SolarizedDarkStyle': ('pygments.styles.solarized', 'solarized-dark', ()),
    'SolarizedLightStyle': ('pygments.styles.solarized', 'solarized-light', ()),
    'StarofficeStyle': ('pygments.styles.staroffice', 'staroffice', ()),
    'StataDarkStyle': ('pygments.styles.stata_dark', 'stata-dark', ()),
    'StataLightStyle': ('pygments.styles.stata_light', 'stata-light', ()),
    'TangoStyle': ('pygments.styles.tango', 'tango', ()),
    'TracStyle': ('pygments.styles.trac', 'trac', ()),
    'VimStyle': ('pygments.styles.vim', 'vim', ()),
    'VisualStudioStyle': ('pygments.styles.vs', 'vs', ()),
    'XcodeStyle': ('pygments.styles.xcode', 'xcode', ()),
    'ZenburnStyle': ('pygments.styles.zenburn', 'zenburn', ()),
}
