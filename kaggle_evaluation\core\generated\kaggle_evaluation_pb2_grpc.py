# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import kaggle_evaluation_pb2 as kaggle__evaluation__pb2


class KaggleEvaluationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Send = channel.unary_unary(
                '/kaggle_evaluation_client.KaggleEvaluationService/Send',
                request_serializer=kaggle__evaluation__pb2.KaggleEvaluationRequest.SerializeToString,
                response_deserializer=kaggle__evaluation__pb2.KaggleEvaluationResponse.FromString,
                )


class KaggleEvaluationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Send(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_KaggleEvaluationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Send': grpc.unary_unary_rpc_method_handler(
                    servicer.Send,
                    request_deserializer=kaggle__evaluation__pb2.KaggleEvaluationRequest.FromString,
                    response_serializer=kaggle__evaluation__pb2.KaggleEvaluationResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'kaggle_evaluation_client.KaggleEvaluationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class KaggleEvaluationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Send(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/kaggle_evaluation_client.KaggleEvaluationService/Send',
            kaggle__evaluation__pb2.KaggleEvaluationRequest.SerializeToString,
            kaggle__evaluation__pb2.KaggleEvaluationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
